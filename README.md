# 经典扫雷 - Classic Minesweeper

🎮 完美复刻Windows经典扫雷游戏，部署在Cloudflare Workers上

## 🚀 在线体验

**立即游戏：** [部署后将显示您的Workers域名]

## ✨ 特性

### 🎮 核心游戏功能
- 🎯 **经典体验**：完美还原Windows扫雷的3D视觉效果和交互方式
- 🎮 **三种难度**：初级(9x9)、中级(16x16)、专家(30x16)

### 🎨 现代化界面
- 🌙 **深色主题设计**：护眼的深色配色方案
- ✨ **毛玻璃效果**：现代化的视觉设计
- 🎭 **精美动画**：流畅的交互动画和反馈
- 📐 **智能响应式**：自动计算最佳格子大小，完美适配任何屏幕尺寸
- 🎨 **精美UI**：自定义游戏结束提示框，告别丑陋的原生alert

### 🏆 智能排行榜系统
- 📊 **实时排行榜**：支持三种难度的独立排行榜
- 🧠 **智能成绩对比**：自动对比历史最佳成绩
- 🛡️ **防重复上传**：智能过滤相同或更差的成绩
- 🎉 **个性化反馈**：新纪录庆祝、首次上传欢迎等

### ⚡ 技术特性
- 🚀 **零依赖**：纯HTML5 + CSS3 + JavaScript实现
- ☁️ **云端部署**：基于Cloudflare Workers，全球CDN加速
- 📱 **全设备支持**：从手机到大屏显示器，完美适配
- ⚡ **极速加载**：单文件部署，秒开体验

## 🎯 游戏规则

- **左键点击**：挖掘格子
- **右键点击**：标记/取消标记地雷
- **双键快速挖掘**：在已揭开的数字上同时按左右键，快速挖掘周围格子（当标记数等于数字时生效）
- **目标**：找出所有地雷而不踩到它们
- **数字**：表示周围8个格子中地雷的数量
- **首次点击保护**：第一次点击永远不会是地雷

## 🚀 快速开始

### 本地开发

1. 克隆项目
```bash
git clone https://github.com/kadidalax/cf-minesweeper.git
cd cf-minesweeper
```

2. 安装依赖
```bash
npm install
```

3. 本地运行
```bash
npm run dev
```

4. 访问 `http://localhost:8787` 开始游戏

### 部署到Cloudflare Workers

1. 登录Cloudflare账户
```bash
npx wrangler login
```

2. 创建KV命名空间
```bash
npx wrangler kv:namespace create "LEADERBOARD"
```

3. 更新wrangler.toml配置
将步骤2返回的KV命名空间ID复制到`wrangler.toml`文件中：
```toml
[[kv_namespaces]]
binding = "LEADERBOARD"
id = "your-actual-kv-namespace-id"
preview_id = "your-actual-preview-id"
```

4. 部署项目
```bash
npm run deploy
```

## 🛠️ 技术栈

- **前端**：HTML5, CSS3, JavaScript (ES6+)
- **运行环境**：Cloudflare Workers
- **数据存储**：Cloudflare KV
- **构建工具**：Wrangler CLI
- **部署平台**：Cloudflare Edge Network

## 🎨 设计亮点

### 🎯 游戏体验
- **完美居中布局**：游戏区域智能定位在屏幕中央偏上
- **智能边界保护**：动态计算确保游戏区域不超出屏幕边界
- **双键快速挖掘**：完整实现经典扫雷的高级交互功能
- **右键菜单禁用**：多重保护机制，确保专业游戏体验

### 🎨 视觉设计
- **深色主题**：现代化的深色配色方案，护眼舒适
- **毛玻璃效果**：backdrop-filter 实现的现代视觉效果
- **格子状态区分**：未挖掘(深色金属质感) vs 已挖掘(浅色纸质感)
- **精美动画**：地雷爆炸、旗帜挥舞、快速挖掘高亮等

### 🧠 智能系统
- **智能成绩对比**：自动检测新纪录、成绩下降、首次上传等情况
- **个性化反馈**：根据不同情况提供相应的庆祝或鼓励信息
- **防重复上传**：智能过滤相同或更差的成绩，保护排行榜质量

### ⚡ 技术实现
- **Fisher-Yates洗牌**：确保地雷随机分布
- **BFS自动展开**：点击空白区域自动展开相邻格子
- **智能响应式布局**：动态计算最佳格子大小，自适应屏幕尺寸
- **性能优化**：事件委托和批量DOM更新

## 📝 开发日志

### 🎯 核心功能开发
- ✅ 项目初始化和基础架构
- ✅ 经典UI界面实现
- ✅ 游戏逻辑核心算法
- ✅ 交互功能完整实现
- ✅ 双键快速挖掘功能

### 🎨 界面优化升级
- ✅ 深色主题重设计
- ✅ 毛玻璃效果和现代化视觉
- ✅ 格子状态区分度大幅提升
- ✅ 完美居中布局系统
- ✅ 智能边界保护机制

### 🏆 排行榜系统
- ✅ Cloudflare KV 数据存储
- ✅ 实时排行榜功能
- ✅ 智能成绩对比系统
- ✅ 个性化用户反馈

### ⚡ 用户体验优化
- ✅ 智能响应式布局系统
- ✅ 右键菜单完全禁用
- ✅ 移动端触摸支持
- ✅ 游戏状态管理和优化
- ✅ 精美模态框和动画效果

### 🚀 部署和发布
- ✅ Cloudflare Workers部署
- ✅ 项目文档完善
- ✅ 代码优化和清理

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License
